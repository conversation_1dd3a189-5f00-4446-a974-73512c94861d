#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析Excel文件结构的脚本
"""

import pandas as pd
import numpy as np

def analyze_excel_structure(file_path):
    """分析Excel文件的结构"""
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        
        print("=" * 50)
        print("Excel文件结构分析")
        print("=" * 50)
        
        # 基本信息
        print(f"文件路径: {file_path}")
        print(f"数据行数: {len(df)}")
        print(f"数据列数: {len(df.columns)}")
        print()
        
        # 列名信息
        print("列名列表:")
        for i, col in enumerate(df.columns, 1):
            print(f"{i:2d}. {col}")
        print()
        
        # 数据类型
        print("数据类型:")
        for col in df.columns:
            print(f"{col}: {df[col].dtype}")
        print()
        
        # 前几行数据预览
        print("前5行数据预览:")
        print(df.head())
        print()
        
        # 检查是否有季节标签相关的列
        season_related_cols = []
        for col in df.columns:
            if any(keyword in col.lower() for keyword in ['季节', 'season', '标签', 'label', 'tag']):
                season_related_cols.append(col)
        
        if season_related_cols:
            print("发现季节相关列:")
            for col in season_related_cols:
                print(f"- {col}")
                unique_values = df[col].unique()
                print(f"  唯一值: {unique_values}")
                print(f"  值计数:")
                print(df[col].value_counts())
                print()
        
        # 检查缺失值
        print("缺失值统计:")
        missing_data = df.isnull().sum()
        for col, missing_count in missing_data.items():
            if missing_count > 0:
                print(f"{col}: {missing_count} ({missing_count/len(df)*100:.1f}%)")
        
        if missing_data.sum() == 0:
            print("没有发现缺失值")
        
        return df
        
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return None

if __name__ == "__main__":
    file_path = "上架商品季节标签.xlsx"
    df = analyze_excel_structure(file_path)
