#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
季节标签分析配置文件
"""

# 季节标签映射
SEASON_LABELS = {
    'SS': '春夏',
    'AW': '秋冬', 
    '全季': '全季'
}

# 大模型分析提示词模板
SEASON_ANALYSIS_PROMPT = """
你是一个专业的商品季节性分析专家。请根据商品信息判断该商品最适合的季节标签。

季节标签定义：
- SS（春夏）：适合春季和夏季使用的商品，如夏装、防晒用品、清爽护肤品等
- AW（秋冬）：适合秋季和冬季使用的商品，如冬装、保暖用品、滋润护肤品等  
- 全季：一年四季都适用的商品，如基础护肤品、内衣、日用品等

商品信息：
商品名称：{product_name}
商品描述：{product_description}
商品类目：{category_info}

请仔细分析商品的特性、用途和季节性特征，然后给出最合适的季节标签。

要求：
1. 只返回一个季节标签：SS、AW 或 全季
2. 不要包含任何解释或其他文字
3. 如果商品明显具有季节性特征，优先选择SS或AW
4. 如果商品没有明显季节性或四季都适用，选择全季

季节标签："""

# 批量处理配置
BATCH_SIZE = 10  # 每批处理的商品数量
MAX_RETRIES = 3  # 最大重试次数
DELAY_BETWEEN_REQUESTS = 1  # 请求间隔（秒）

# 输出文件配置
OUTPUT_FILE = "season_analysis_results.xlsx"
REPORT_FILE = "accuracy_report.txt"

# 大模型API配置（需要根据实际使用的模型调整）
MODEL_CONFIG = {
    'model_name': 'gpt-3.5-turbo',  # 可以改为其他模型
    'temperature': 0.1,  # 低温度确保结果稳定
    'max_tokens': 10,    # 只需要返回标签
}

def format_category_info(row):
    """格式化类目信息"""
    categories = []
    for col in ['前端默认一级类目', '物理一级类目', '物理二级类目', '物理三级类目', '物理四级类目']:
        if pd.notna(row[col]) and row[col] != '':
            categories.append(str(row[col]))
    return ' > '.join(categories)

def create_analysis_prompt(row):
    """创建分析提示词"""
    category_info = format_category_info(row)
    
    return SEASON_ANALYSIS_PROMPT.format(
        product_name=row['商品名'],
        product_description=row['商品描述'] if pd.notna(row['商品描述']) else '无描述',
        category_info=category_info if category_info else '无类目信息'
    )

# 准确率计算相关
def calculate_accuracy_metrics(y_true, y_pred):
    """计算准确率指标"""
    from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
    import numpy as np

    accuracy = accuracy_score(y_true, y_pred)
    report = classification_report(y_true, y_pred, target_names=['SS', 'AW', '全季'])
    cm = confusion_matrix(y_true, y_pred, labels=['SS', 'AW', '全季'])

    return {
        'accuracy': accuracy,
        'classification_report': report,
        'confusion_matrix': cm
    }

import pandas as pd
