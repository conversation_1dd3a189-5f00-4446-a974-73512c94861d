#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的商品季节标签分析流程
"""

import os
import sys
import pandas as pd
from season_analyzer import MockSeasonAnalyzer
from accuracy_calculator import AccuracyCalculator

def main():
    """运行完整的分析流程"""
    print("=" * 60)
    print("商品季节标签大模型分析系统")
    print("=" * 60)
    
    # 检查输入文件
    excel_file = "上架商品季节标签.xlsx"
    if not os.path.exists(excel_file):
        print(f"错误：找不到输入文件 {excel_file}")
        return False
    
    # 1. 数据概览
    print("\n1. 数据概览")
    print("-" * 30)
    df = pd.read_excel(excel_file)
    print(f"总商品数量: {len(df)}")
    print(f"数据列数: {len(df.columns)}")
    
    # 显示季节标签分布
    print("\n人工标注季节标签分布:")
    label_counts = df['季节标签'].value_counts()
    for label, count in label_counts.items():
        percentage = count / len(df) * 100
        print(f"  {label}: {count} ({percentage:.1f}%)")
    
    # 2. 选择分析样本数量
    print(f"\n2. 选择分析样本")
    print("-" * 30)
    
    # 可以选择分析全部数据或部分数据
    choice = input("选择分析模式 (1: 测试模式-前100个商品, 2: 完整模式-全部商品, 3: 自定义数量): ")
    
    if choice == "1":
        sample_size = min(100, len(df))
        print(f"测试模式：分析前 {sample_size} 个商品")
    elif choice == "2":
        sample_size = len(df)
        print(f"完整模式：分析全部 {sample_size} 个商品")
    elif choice == "3":
        try:
            sample_size = int(input("请输入要分析的商品数量: "))
            sample_size = min(sample_size, len(df))
            print(f"自定义模式：分析前 {sample_size} 个商品")
        except ValueError:
            print("输入无效，使用默认测试模式")
            sample_size = min(100, len(df))
    else:
        print("输入无效，使用默认测试模式")
        sample_size = min(100, len(df))
    
    # 3. 运行季节标签分析
    print(f"\n3. 开始分析 {sample_size} 个商品")
    print("-" * 30)
    
    analyzer = MockSeasonAnalyzer()
    results = analyzer.analyze_batch(df, 0, sample_size)
    
    # 保存分析结果
    results_df = pd.DataFrame(results)
    results_file = "season_analysis_results.xlsx"
    results_df.to_excel(results_file, index=False)
    print(f"分析结果已保存到: {results_file}")
    
    # 4. 计算准确率和生成报告
    print(f"\n4. 计算准确率和生成报告")
    print("-" * 30)
    
    calculator = AccuracyCalculator(results_file)
    analysis_results = calculator.generate_detailed_report()
    
    # 5. 显示结果摘要
    print(f"\n5. 分析结果摘要")
    print("-" * 30)
    print(f"总体准确率: {analysis_results['overall_accuracy']:.4f} ({analysis_results['overall_accuracy']*100:.2f}%)")
    print(f"正确预测: {results_df['是否正确'].sum()}/{len(results_df)}")
    
    print("\n各类别准确率:")
    for label, metrics in analysis_results['class_accuracy'].items():
        print(f"  {label}: {metrics['accuracy']:.4f} ({metrics['accuracy']*100:.2f}%) - {metrics['correct']}/{metrics['total']}")
    
    print("\n预测标签分布:")
    pred_counts = results_df['预测标签'].value_counts()
    for label, count in pred_counts.items():
        percentage = count / len(results_df) * 100
        print(f"  {label}: {count} ({percentage:.1f}%)")
    
    # 6. 生成的文件列表
    print(f"\n6. 生成的文件")
    print("-" * 30)
    generated_files = [
        "season_analysis_results.xlsx",
        "accuracy_report.txt", 
        "confusion_matrix.png",
        "final_analysis_report.md"
    ]
    
    for file in generated_files:
        if os.path.exists(file):
            print(f"✓ {file}")
        else:
            print(f"✗ {file} (未生成)")
    
    print(f"\n分析完成！请查看生成的报告文件。")
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n程序执行成功！")
        else:
            print("\n程序执行失败！")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        sys.exit(1)
