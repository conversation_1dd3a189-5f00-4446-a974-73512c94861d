#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品季节标签分析器
使用大模型分析商品信息并判断季节标签
"""

import pandas as pd
import numpy as np
import time
import json
import re
from typing import List, Dict, Optional
import openai
from season_analysis_config import *

class SeasonAnalyzer:
    def __init__(self, api_key: str = None):
        """初始化季节分析器"""
        self.api_key = api_key
        if api_key:
            openai.api_key = api_key
        self.results = []
        
    def analyze_single_product(self, row: pd.Series) -> str:
        """分析单个商品的季节标签"""
        try:
            # 创建提示词
            prompt = create_analysis_prompt(row)
            
            # 调用大模型API（这里使用OpenAI作为示例）
            response = openai.ChatCompletion.create(
                model=MODEL_CONFIG['model_name'],
                messages=[
                    {"role": "user", "content": prompt}
                ],
                temperature=MODEL_CONFIG['temperature'],
                max_tokens=MODEL_CONFIG['max_tokens']
            )
            
            # 提取结果
            result = response.choices[0].message.content.strip()
            
            # 清理和验证结果
            result = self.clean_result(result)
            
            return result
            
        except Exception as e:
            print(f"分析商品 {row['item_id']} 时出错: {e}")
            return "全季"  # 默认返回全季
    
    def clean_result(self, result: str) -> str:
        """清理和验证分析结果"""
        # 移除多余的空格和换行
        result = result.strip()
        
        # 提取有效的季节标签
        if 'SS' in result.upper():
            return 'SS'
        elif 'AW' in result.upper():
            return 'AW'
        elif '全季' in result:
            return '全季'
        else:
            # 如果没有找到有效标签，尝试从内容推断
            if any(word in result for word in ['春', '夏', '清爽', '防晒']):
                return 'SS'
            elif any(word in result for word in ['秋', '冬', '保暖', '滋润']):
                return 'AW'
            else:
                return '全季'
    
    def analyze_batch(self, df: pd.DataFrame, start_idx: int = 0, end_idx: int = None) -> List[Dict]:
        """批量分析商品"""
        if end_idx is None:
            end_idx = len(df)
        
        results = []
        
        for idx in range(start_idx, min(end_idx, len(df))):
            row = df.iloc[idx]
            
            print(f"正在分析第 {idx + 1}/{len(df)} 个商品: {row['商品名'][:30]}...")
            
            # 分析商品
            predicted_label = self.analyze_single_product(row)
            
            # 记录结果
            result = {
                'item_id': int(row['item_id']),
                '商品名': str(row['商品名']),
                '人工标签': str(row['季节标签']),
                '预测标签': str(predicted_label),
                '是否正确': bool(row['季节标签'] == predicted_label)
            }
            
            results.append(result)
            
            # 添加延迟避免API限制
            time.sleep(DELAY_BETWEEN_REQUESTS)
            
            # 每处理一定数量保存一次结果
            if (idx + 1) % 50 == 0:
                self.save_intermediate_results(results, f"temp_results_{idx + 1}.json")
        
        return results
    
    def save_intermediate_results(self, results: List[Dict], filename: str):
        """保存中间结果"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"中间结果已保存到 {filename}")

# 模拟大模型分析（用于测试，不需要真实API）
class MockSeasonAnalyzer(SeasonAnalyzer):
    def __init__(self):
        """初始化模拟分析器"""
        super().__init__()
        
    def analyze_single_product(self, row: pd.Series) -> str:
        """模拟分析单个商品的季节标签"""
        try:
            # 基于商品名和描述的简单规则判断
            product_name = str(row['商品名']).lower()
            product_desc = str(row['商品描述']).lower() if pd.notna(row['商品描述']) else ""
            
            text = product_name + " " + product_desc
            
            # 春夏关键词
            ss_keywords = ['夏', '春', '防晒', '清爽', '薄', '短袖', '短裤', '凉鞋', '防紫外线', 
                          '透气', '轻薄', '冰丝', '雪纺', '亚麻', '棉麻']
            
            # 秋冬关键词  
            aw_keywords = ['冬', '秋', '保暖', '厚', '羽绒', '毛衣', '长袖', '长裤', '靴子',
                          '滋润', '修复', '加厚', '绒', '棉衣', '大衣', '围巾']
            
            # 计算关键词匹配分数
            ss_score = sum(1 for keyword in ss_keywords if keyword in text)
            aw_score = sum(1 for keyword in aw_keywords if keyword in text)
            
            # 判断季节
            if ss_score > aw_score and ss_score > 0:
                return 'SS'
            elif aw_score > ss_score and aw_score > 0:
                return 'AW'
            else:
                return '全季'
                
        except Exception as e:
            print(f"分析商品 {row['item_id']} 时出错: {e}")
            return "全季"

def main():
    """主函数"""
    print("开始商品季节标签分析...")
    
    # 读取数据
    df = pd.read_excel("上架商品季节标签.xlsx")
    print(f"读取到 {len(df)} 个商品数据")
    
    # 创建分析器（使用模拟分析器进行测试）
    analyzer = MockSeasonAnalyzer()
    
    # 可以选择分析部分数据进行测试
    test_size = min(100, len(df))  # 先分析前100个商品进行测试
    print(f"开始分析前 {test_size} 个商品...")
    
    # 批量分析
    results = analyzer.analyze_batch(df, 0, test_size)
    
    # 保存结果
    results_df = pd.DataFrame(results)
    results_df.to_excel("season_analysis_results.xlsx", index=False)
    print(f"分析结果已保存到 season_analysis_results.xlsx")
    
    return results_df

if __name__ == "__main__":
    results_df = main()
