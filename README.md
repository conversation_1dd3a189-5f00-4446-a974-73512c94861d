# 商品季节标签大模型分析系统

## 项目简介

本项目使用大模型技术分析商品的季节属性，自动判断商品属于春夏(SS)、秋冬(AW)还是全季商品，并与人工标注进行对比分析，计算预测准确率。

## 功能特点

- 📊 **自动分析**：基于商品名称、描述和类目信息自动判断季节标签
- 🎯 **准确率评估**：与人工标注对比，计算详细的准确率指标
- 📈 **可视化报告**：生成混淆矩阵图和详细分析报告
- 🔧 **灵活配置**：支持测试模式和完整分析模式

## 季节标签定义

- **SS（春夏）**：适合春季和夏季使用的商品，如夏装、防晒用品、清爽护肤品等
- **AW（秋冬）**：适合秋季和冬季使用的商品，如冬装、保暖用品、滋润护肤品等  
- **全季**：一年四季都适用的商品，如基础护肤品、内衣、日用品等

## 文件结构

```
├── 上架商品季节标签.xlsx          # 输入数据文件
├── run_complete_analysis.py       # 主程序入口
├── season_analyzer.py             # 季节标签分析器
├── accuracy_calculator.py         # 准确率计算器
├── season_analysis_config.py      # 配置文件
├── analyze_excel_structure.py     # 数据结构分析
├── final_analysis_report.md       # 最终分析报告
└── README.md                      # 使用说明
```

## 快速开始

### 1. 环境要求

```bash
pip install pandas numpy matplotlib scikit-learn openpyxl
```

### 2. 运行分析

```bash
python run_complete_analysis.py
```

### 3. 选择分析模式

程序会提示选择分析模式：
- **模式1**：测试模式 - 分析前100个商品（推荐用于快速测试）
- **模式2**：完整模式 - 分析全部商品（需要较长时间）
- **模式3**：自定义模式 - 指定分析数量

### 4. 查看结果

分析完成后会生成以下文件：
- `season_analysis_results.xlsx` - 详细分析结果
- `accuracy_report.txt` - 准确率分析报告
- `confusion_matrix.png` - 混淆矩阵可视化图
- `final_analysis_report.md` - 完整分析报告

## 分析结果示例

基于前100个商品的测试结果：

### 总体表现
- **总体准确率**: 76.00%
- **分析商品数**: 100个
- **正确预测**: 76个

### 各类别表现
| 真实标签 | 预测准确率 | 样本数 |
|---------|-----------|--------|
| AW（秋冬） | 100.00% | 2 |
| 全季 | 75.51% | 98 |
| SS（春夏） | - | 0 |

### 预测分布
| 预测标签 | 数量 | 占比 |
|---------|------|------|
| 全季 | 74 | 74.0% |
| AW（秋冬） | 16 | 16.0% |
| SS（春夏） | 10 | 10.0% |

## 技术实现

### 分析方法
1. **关键词匹配**：基于季节性关键词进行初步判断
2. **语义分析**：分析商品名称和描述的语义特征
3. **类目参考**：结合商品类目信息辅助判断

### 关键词库
- **春夏关键词**：夏、春、防晒、清爽、薄、短袖、透气等
- **秋冬关键词**：冬、秋、保暖、厚、羽绒、长袖、滋润等

## 改进方向

### 当前版本限制
- 使用简化的规则匹配模拟大模型
- 对复杂语义理解有限
- 样本分布不均衡影响评估

### 未来改进
- 集成真实的大语言模型API（如GPT-4、Claude等）
- 增加更复杂的语义理解能力
- 优化关键词权重和上下文分析
- 支持多模态分析（文本+图片）

## 使用注意事项

1. **数据格式**：确保Excel文件包含必要的列（商品名、商品描述、季节标签等）
2. **分析时间**：完整模式分析7000+商品可能需要较长时间
3. **结果解释**：当前使用模拟分析器，实际大模型效果可能不同
4. **样本均衡**：建议使用更均衡的测试样本进行评估

## 联系方式

如有问题或建议，请联系项目维护者。

---

*最后更新：2025年6月25日*
