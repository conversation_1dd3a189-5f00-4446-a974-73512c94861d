#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
准确率计算和报告生成器
"""

import pandas as pd
import numpy as np
try:
    import matplotlib.pyplot as plt
    import matplotlib
    matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']  # 支持中文显示
    matplotlib.rcParams['axes.unicode_minus'] = False
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("警告: matplotlib未安装，将跳过图表生成")

try:
    from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("警告: sklearn未安装，将使用简化的准确率计算")

from season_analysis_config import calculate_accuracy_metrics

class AccuracyCalculator:
    def __init__(self, results_file: str):
        """初始化准确率计算器"""
        self.results_df = pd.read_excel(results_file)
        self.labels = ['SS', 'AW', '全季']
        
    def calculate_overall_accuracy(self):
        """计算总体准确率"""
        y_true = self.results_df['人工标签'].tolist()
        y_pred = self.results_df['预测标签'].tolist()

        if SKLEARN_AVAILABLE:
            metrics = calculate_accuracy_metrics(y_true, y_pred)
        else:
            # 简化版准确率计算
            correct = sum(1 for t, p in zip(y_true, y_pred) if t == p)
            total = len(y_true)
            accuracy = correct / total if total > 0 else 0

            # 简化的混淆矩阵
            cm = [[0 for _ in self.labels] for _ in self.labels]
            for t, p in zip(y_true, y_pred):
                if t in self.labels and p in self.labels:
                    i = self.labels.index(t)
                    j = self.labels.index(p)
                    cm[i][j] += 1

            metrics = {
                'accuracy': accuracy,
                'classification_report': f"总体准确率: {accuracy:.4f}",
                'confusion_matrix': cm
            }

        return metrics
    
    def calculate_class_wise_accuracy(self):
        """计算各类别的准确率"""
        class_accuracy = {}
        
        for label in self.labels:
            # 获取该类别的真实标签和预测标签
            true_mask = self.results_df['人工标签'] == label
            if true_mask.sum() > 0:
                correct_predictions = (
                    (self.results_df['人工标签'] == label) & 
                    (self.results_df['预测标签'] == label)
                ).sum()
                
                total_true = true_mask.sum()
                accuracy = correct_predictions / total_true
                
                class_accuracy[label] = {
                    'accuracy': accuracy,
                    'correct': correct_predictions,
                    'total': total_true
                }
        
        return class_accuracy
    
    def generate_confusion_matrix_plot(self, save_path: str = "confusion_matrix.png"):
        """生成混淆矩阵图"""
        if not SKLEARN_AVAILABLE:
            print("sklearn未安装，跳过混淆矩阵计算")
            return None

        y_true = self.results_df['人工标签'].tolist()
        y_pred = self.results_df['预测标签'].tolist()

        cm = confusion_matrix(y_true, y_pred, labels=self.labels)

        if MATPLOTLIB_AVAILABLE:
            plt.figure(figsize=(8, 6))
            # 手动绘制热力图，不使用seaborn
            im = plt.imshow(cm, interpolation='nearest', cmap='Blues')
            plt.title('季节标签预测混淆矩阵')
            plt.colorbar(im)

            # 添加数值标注
            for i in range(len(self.labels)):
                for j in range(len(self.labels)):
                    plt.text(j, i, str(cm[i, j]), ha="center", va="center")

            plt.xlabel('预测标签')
            plt.ylabel('真实标签')
            plt.xticks(range(len(self.labels)), self.labels)
            plt.yticks(range(len(self.labels)), self.labels)
            plt.tight_layout()
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
        else:
            print("matplotlib未安装，跳过图表生成")

        return cm
    
    def analyze_error_cases(self):
        """分析错误案例"""
        error_cases = self.results_df[self.results_df['是否正确'] == False].copy()
        
        if len(error_cases) == 0:
            return {"message": "没有发现错误案例"}
        
        # 按错误类型分组
        error_analysis = {}
        
        for _, row in error_cases.iterrows():
            error_type = f"{row['人工标签']} -> {row['预测标签']}"
            if error_type not in error_analysis:
                error_analysis[error_type] = []
            
            error_analysis[error_type].append({
                'item_id': row['item_id'],
                '商品名': row['商品名']
            })
        
        return error_analysis
    
    def generate_detailed_report(self, output_file: str = "accuracy_report.txt"):
        """生成详细的准确率报告"""
        # 计算各种指标
        overall_metrics = self.calculate_overall_accuracy()
        class_accuracy = self.calculate_class_wise_accuracy()
        error_analysis = self.analyze_error_cases()
        
        # 生成混淆矩阵图
        cm = self.generate_confusion_matrix_plot()
        
        # 写入报告
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("商品季节标签预测准确率分析报告\n")
            f.write("=" * 60 + "\n\n")
            
            # 基本信息
            f.write("1. 基本信息\n")
            f.write("-" * 30 + "\n")
            f.write(f"总商品数量: {len(self.results_df)}\n")
            f.write(f"正确预测数量: {self.results_df['是否正确'].sum()}\n")
            f.write(f"错误预测数量: {(~self.results_df['是否正确']).sum()}\n\n")
            
            # 总体准确率
            f.write("2. 总体准确率\n")
            f.write("-" * 30 + "\n")
            f.write(f"总体准确率: {overall_metrics['accuracy']:.4f} ({overall_metrics['accuracy']*100:.2f}%)\n\n")
            
            # 各类别准确率
            f.write("3. 各类别准确率\n")
            f.write("-" * 30 + "\n")
            for label, metrics in class_accuracy.items():
                f.write(f"{label}: {metrics['accuracy']:.4f} ({metrics['accuracy']*100:.2f}%) ")
                f.write(f"({metrics['correct']}/{metrics['total']})\n")
            f.write("\n")
            
            # 混淆矩阵
            f.write("4. 混淆矩阵\n")
            f.write("-" * 30 + "\n")
            f.write("        预测标签\n")
            f.write("真实\\   SS    AW    全季\n")
            for i, true_label in enumerate(self.labels):
                f.write(f"{true_label:4s}  ")
                for j in range(len(self.labels)):
                    f.write(f"{cm[i][j]:4d}  ")
                f.write("\n")
            f.write("\n")
            
            # 详细分类报告
            f.write("5. 详细分类报告\n")
            f.write("-" * 30 + "\n")
            f.write(overall_metrics['classification_report'])
            f.write("\n\n")
            
            # 错误案例分析
            f.write("6. 错误案例分析\n")
            f.write("-" * 30 + "\n")
            if isinstance(error_analysis, dict) and "message" in error_analysis:
                f.write(error_analysis["message"] + "\n")
            else:
                for error_type, cases in error_analysis.items():
                    f.write(f"\n{error_type} ({len(cases)}个案例):\n")
                    for case in cases[:5]:  # 只显示前5个案例
                        f.write(f"  - {case['item_id']}: {case['商品名'][:50]}...\n")
                    if len(cases) > 5:
                        f.write(f"  ... 还有{len(cases)-5}个案例\n")
            
            f.write("\n" + "=" * 60 + "\n")
            f.write("报告生成完成\n")
        
        print(f"详细报告已保存到: {output_file}")
        print(f"混淆矩阵图已保存到: confusion_matrix.png")
        
        return {
            'overall_accuracy': overall_metrics['accuracy'],
            'class_accuracy': class_accuracy,
            'confusion_matrix': cm,
            'error_analysis': error_analysis
        }

def main():
    """主函数"""
    try:
        # 创建准确率计算器
        calculator = AccuracyCalculator("season_analysis_results.xlsx")
        
        # 生成详细报告
        results = calculator.generate_detailed_report()
        
        # 打印简要结果
        print("\n" + "=" * 50)
        print("分析结果摘要:")
        print("=" * 50)
        print(f"总体准确率: {results['overall_accuracy']:.4f} ({results['overall_accuracy']*100:.2f}%)")
        print("\n各类别准确率:")
        for label, metrics in results['class_accuracy'].items():
            print(f"  {label}: {metrics['accuracy']:.4f} ({metrics['accuracy']*100:.2f}%)")
        
        return results
        
    except FileNotFoundError:
        print("错误: 找不到分析结果文件 'season_analysis_results.xlsx'")
        print("请先运行 season_analyzer.py 生成分析结果")
        return None

if __name__ == "__main__":
    results = main()
